import "https://deno.land/x/xhr@0.1.0/mod.ts";
import { serve } from "https://deno.land/std@0.168.0/http/server.ts";

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
};

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    const { audioBlob, language = 'it' } = await req.json();
    
    if (!audioBlob) {
      throw new Error('No audio data provided');
    }

    const OPENAI_API_KEY = Deno.env.get('OPENAI_API_KEY');
    if (!OPENAI_API_KEY) {
      throw new Error('OPENAI_API_KEY not configured');
    }

    // Step 1: Transcribe audio
    const binaryAudio = Uint8Array.from(atob(audioBlob), c => c.charCodeAt(0));
    const formData = new FormData();
    const blob = new Blob([binaryAudio], { type: 'audio/webm' });
    formData.append('file', blob, 'audio.webm');
    formData.append('model', 'whisper-1');
    formData.append('language', language);

    const transcriptionResponse = await fetch('https://api.openai.com/v1/audio/transcriptions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${OPENAI_API_KEY}`,
      },
      body: formData,
    });

    if (!transcriptionResponse.ok) {
      throw new Error(`Transcription failed: ${await transcriptionResponse.text()}`);
    }

    const { text: transcript } = await transcriptionResponse.json();

    // Step 2: Generate title and summary
    const completionResponse = await fetch('https://api.openai.com/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${OPENAI_API_KEY}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: 'gpt-4o-mini',
        messages: [
          {
            role: 'system',
            content: 'Sei un assistente che genera titoli e sommari concisi da trascrizioni. Rispondi SOLO in formato JSON con le chiavi "title" e "summary".'
          },
          {
            role: 'user',
            content: `Genera un titolo breve (massimo 6 parole) e un sommario conciso (massimo 2 frasi ed un totale di 15 parole) per questa trascrizione:\n\n${transcript}`
          }
        ],
        temperature: 0.7,
        max_tokens: 200,
      }),
    });

    if (!completionResponse.ok) {
      throw new Error(`Completion failed: ${await completionResponse.text()}`);
    }

    const completionData = await completionResponse.json();
    const content = completionData.choices[0].message.content;
    
    // Parse JSON response
    let title = 'Nota Vocale';
    let summary = transcript.substring(0, 100) + '...';
    
    try {
      const parsed = JSON.parse(content);
      title = parsed.title || title;
      summary = parsed.summary || summary;
    } catch (parseError) {
      console.error('Failed to parse AI response, using defaults:', parseError);
    }

    return new Response(
      JSON.stringify({ 
        transcript,
        title,
        summary 
      }),
      { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    );

  } catch (error) {
    console.error('Error in process-note-transcript:', error);
    return new Response(
      JSON.stringify({ 
        error: error instanceof Error ? error.message : 'Unknown error' 
      }),
      {
        status: 500,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      }
    );
  }
});
