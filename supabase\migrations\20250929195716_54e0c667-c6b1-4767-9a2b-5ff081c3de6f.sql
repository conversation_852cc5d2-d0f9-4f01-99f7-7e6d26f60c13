-- Create notes table for voice and text notes
CREATE TABLE public.notes (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  title TEXT NOT NULL,
  content TEXT,
  transcript TEXT,
  audio_url TEXT,
  audio_duration INTEGER, -- Duration in seconds
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Add Row Level Security (RLS)
ALTER TABLE public.notes ENABLE ROW LEVEL SECURITY;

-- Create policies for RLS
CREATE POLICY "Users can view their own notes" 
  ON public.notes 
  FOR SELECT 
  USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own notes" 
  ON public.notes 
  FOR INSERT 
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own notes" 
  ON public.notes 
  FOR UPDATE 
  USING (auth.uid() = user_id)
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can delete their own notes" 
  ON public.notes 
  FOR DELETE 
  USING (auth.uid() = user_id);

-- Create indexes for better performance
CREATE INDEX idx_notes_user_id ON public.notes(user_id);
CREATE INDEX idx_notes_created_at ON public.notes(created_at DESC);
CREATE INDEX idx_notes_title ON public.notes USING gin(to_tsvector('english', title));
CREATE INDEX idx_notes_content ON public.notes USING gin(to_tsvector('english', content));
CREATE INDEX idx_notes_transcript ON public.notes USING gin(to_tsvector('english', transcript));

-- Create function to automatically update updated_at timestamp
CREATE OR REPLACE FUNCTION update_notes_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = now();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to automatically update updated_at
CREATE TRIGGER update_notes_updated_at_trigger
  BEFORE UPDATE ON public.notes
  FOR EACH ROW
  EXECUTE FUNCTION update_notes_updated_at();

-- Add comments for documentation
COMMENT ON TABLE public.notes IS 'Table for storing user notes with optional voice recordings and transcripts';
COMMENT ON COLUMN public.notes.title IS 'Title of the note';
COMMENT ON COLUMN public.notes.content IS 'Text content of the note';
COMMENT ON COLUMN public.notes.transcript IS 'Transcribed text from voice recording';
COMMENT ON COLUMN public.notes.audio_url IS 'URL to the stored audio file';
COMMENT ON COLUMN public.notes.audio_duration IS 'Duration of audio recording in seconds';