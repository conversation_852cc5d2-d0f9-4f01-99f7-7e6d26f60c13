import { useState, useEffect } from "react";
import { Mic, Play, Pause, Square, Save, X, Trash2, FileText, Loader2, ChevronDown, ChevronUp } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Badge } from "@/components/ui/badge";
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible";
import { useVoiceRecording } from "@/hooks/useVoiceRecording";
import { toast } from "sonner";
import { motion, AnimatePresence } from "framer-motion";
import { supabase } from "@/integrations/supabase/client";

interface NoteRecordingDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSave: (noteData: {
    title: string;
    content?: string;
    audioBlob?: Blob;
    duration?: number;
    transcript?: string;
  }) => Promise<void>;
  initialTitle?: string;
  initialContent?: string;
}

export default function NoteRecordingDialog({
  open,
  onOpenChange,
  onSave,

}: NoteRecordingDialogProps) {


  const [transcript, setTranscript] = useState("");
  const [title, setTitle] = useState("");
  const [summary, setSummary] = useState("");
  const [isSaving, setIsSaving] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [isPlaying, setIsPlaying] = useState(false);
  const [audioElement, setAudioElement] = useState<HTMLAudioElement | null>(null);
  const [isTranscriptOpen, setIsTranscriptOpen] = useState(false);

  const {
    isRecording,
    isPaused,
    duration,
    audioBlob,
    audioUrl,
    error,
    startRecording,
    stopRecording,
    pauseRecording,
    resumeRecording,
    resetRecording,
  } = useVoiceRecording();

  // Format duration to MM:SS
  const formatDuration = (seconds: number): string => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  // Handle audio playback
  const handlePlayPause = () => {
    if (!audioUrl) return;

    if (!audioElement) {
      const audio = new Audio(audioUrl);
      audio.onended = () => setIsPlaying(false);
      audio.onerror = () => {
        toast.error("Errore nella riproduzione audio");
        setIsPlaying(false);
      };
      setAudioElement(audio);
      audio.play();
      setIsPlaying(true);
    } else {
      if (isPlaying) {
        audioElement.pause();
        setIsPlaying(false);
      } else {
        audioElement.play();
        setIsPlaying(true);
      }
    }
  };

  // Handle AI processing (transcription + title + summary)
  const handleProcessWithAI = async () => {
    if (!audioBlob) return;

    try {
      setIsProcessing(true);
      
      // Convert blob to base64
      const reader = new FileReader();
      const base64Promise = new Promise<string>((resolve, reject) => {
        reader.onloadend = () => {
          const base64 = (reader.result as string).split(',')[1];
          resolve(base64);
        };
        reader.onerror = reject;
      });
      reader.readAsDataURL(audioBlob);
      const base64Audio = await base64Promise;

      // Call the new edge function
      const { data, error } = await supabase.functions.invoke('process-note-transcript', {
        body: { audioBlob: base64Audio, language: 'it' }
      });

      if (error) throw error;

      setTranscript(data.transcript);
      setTitle(data.title);
      setSummary(data.summary);
      toast.success("Elaborazione completata");
    } catch (error) {
      console.error("Processing error:", error);
      toast.error("Errore nell'elaborazione");
    } finally {
      setIsProcessing(false);
    }
  };

  // Handle save
  const handleSave = async () => {
    try {
      setIsSaving(true);
      await onSave({
        title: title.trim() || "Nota Vocale",
        content: summary.trim() || undefined,
        audioBlob: audioBlob || undefined,
        duration: audioBlob ? duration : undefined,
        transcript: transcript.trim() || undefined,
      });

      toast.success("Nota salvata con successo");
      handleClose();
    } catch (error) {
      console.error("Error saving note:", error);
      toast.error("Errore nel salvataggio della nota");
    } finally {
      setIsSaving(false);
    }
  };

  // Handle close
  const handleClose = () => {
    if (audioElement) {
      audioElement.pause();
      setAudioElement(null);
    }
    setIsPlaying(false);
    resetRecording();
    setTranscript("");
    setTitle("");
    setSummary("");
    onOpenChange(false);
  };

  // Handle recording toggle
  const handleRecordingToggle = () => {
    if (isRecording) {
      if (isPaused) {
        resumeRecording();
      } else {
        pauseRecording();
      }
    } else {
      if (audioBlob) {
        // If there's already a recording, reset it
        resetRecording();
        if (audioElement) {
          audioElement.pause();
          setAudioElement(null);
          setIsPlaying(false);
        }
      }
      startRecording();
    }
  };

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (audioElement) {
        audioElement.pause();
      }
    };
  }, [audioElement]);

  // Reset form when dialog opens
  useEffect(() => {
    if (open) {
      setTranscript("");
      setTitle("");
      setSummary("");
    }
  }, [open]);

  const hasRecording = audioBlob !== null;
  const canSave = hasRecording && title.trim().length > 0;

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-md w-[95vw] max-h-[95vh] flex flex-col p-0">
        <DialogHeader className="px-6 py-4 border-b shrink-0">
          <DialogTitle>Nuova Nota Vocale</DialogTitle>
        </DialogHeader>

        <div className="flex-1 overflow-y-auto px-4 sm:px-6 py-4">
          <div className="space-y-4">

          {/* Recording Section */}
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">Registrazione vocale</span>
              {hasRecording && (
                <Badge variant="secondary" className="text-xs">
                  {formatDuration(duration)}
                </Badge>
              )}
            </div>

            {/* Recording Controls */}
            <div className="flex items-center gap-2">
              <Button
                variant={isRecording ? "destructive" : "default"}
                size="default"
                onClick={handleRecordingToggle}
                className="flex-1 h-10 text-sm"
                disabled={isSaving}
              >
                <AnimatePresence mode="wait">
                  {isRecording ? (
                    <motion.div
                      key="recording"
                      initial={{ scale: 0 }}
                      animate={{ scale: 1 }}
                      exit={{ scale: 0 }}
                      className="flex items-center gap-2"
                    >
                      {isPaused ? <Play className="h-4 w-4" /> : <Pause className="h-4 w-4" />}
                      {isPaused ? "Riprendi" : "Pausa"}
                    </motion.div>
                  ) : (
                    <motion.div
                      key="not-recording"
                      initial={{ scale: 0 }}
                      animate={{ scale: 1 }}
                      exit={{ scale: 0 }}
                      className="flex items-center gap-2"
                    >
                      <Mic className="h-4 w-4" />
                      {hasRecording ? "Nuova Registrazione" : "Inizia Registrazione"}
                    </motion.div>
                  )}
                </AnimatePresence>
              </Button>

              {isRecording && (
                <Button
                  variant="outline"
                  size="default"
                  onClick={stopRecording}
                  disabled={isSaving}
                  className="h-10 w-10 p-0"
                >
                  <Square className="h-4 w-4" />
                </Button>
              )}

              {hasRecording && !isRecording && (
                <>
                  <Button
                    variant="outline"
                    size="default"
                    onClick={handlePlayPause}
                    disabled={isSaving}
                    className="h-10 w-10 p-0"
                  >
                    {isPlaying ? <Pause className="h-4 w-4" /> : <Play className="h-4 w-4" />}
                  </Button>
                  <Button
                    variant="outline"
                    size="default"
                    onClick={resetRecording}
                    disabled={isSaving}
                    className="h-10 w-10 p-0"
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </>
              )}
            </div>

            {/* Recording Progress */}
            {isRecording && (
              <div className="space-y-2">
                <div className="flex items-center justify-between text-sm">
                  <span className="text-muted-foreground">
                    {isPaused ? "In pausa" : "Registrando..."}
                  </span>
                  <span className="font-mono">{formatDuration(duration)}</span>
                </div>
                <div className="flex items-center gap-2">
                  <div className="flex-1 h-2 bg-muted rounded-full overflow-hidden">
                    <motion.div
                      className="h-full bg-red-500"
                      animate={{
                        opacity: isPaused ? 0.5 : [0.5, 1, 0.5],
                      }}
                      transition={{
                        duration: isPaused ? 0 : 1,
                        repeat: isPaused ? 0 : Infinity,
                        ease: "easeInOut",
                      }}
                      style={{ width: "100%" }}
                    />
                  </div>
                </div>
              </div>
            )}

            {/* Error Display */}
            {error && (
              <div className="text-sm text-destructive bg-destructive/10 p-2 rounded">
                {error}
              </div>
            )}
          </div>

          {/* AI Processing Section */}
          {hasRecording && !isRecording && (
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Elaborazione AI</span>
                {transcript && (
                  <Badge variant="secondary" className="text-xs">
                    <FileText className="h-3 w-3 mr-1" />
                    Completata
                  </Badge>
                )}
              </div>

              {transcript ? (
                <div className="space-y-3">
                  <div className="space-y-2">
                    <label className="text-xs text-muted-foreground">Titolo</label>
                    <Input
                      value={title}
                      onChange={(e) => setTitle(e.target.value)}
                      placeholder="Titolo della nota..."
                      className="text-sm"
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <label className="text-xs text-muted-foreground">Sommario</label>
                    <Textarea
                      value={summary}
                      onChange={(e) => setSummary(e.target.value)}
                      placeholder="Sommario della nota..."
                      rows={2}
                      className="resize-none text-sm min-h-[60px]"
                    />
                  </div>

                  <Collapsible open={isTranscriptOpen} onOpenChange={setIsTranscriptOpen}>
                    <CollapsibleTrigger asChild>
                      <Button
                        variant="ghost"
                        size="sm"
                        className="w-full justify-between p-2 h-auto"
                      >
                        <span className="text-xs text-muted-foreground">Trascrizione completa</span>
                        {isTranscriptOpen ? (
                          <ChevronUp className="h-4 w-4" />
                        ) : (
                          <ChevronDown className="h-4 w-4" />
                        )}
                      </Button>
                    </CollapsibleTrigger>
                    <CollapsibleContent className="pt-2">
                      <Textarea
                        value={transcript}
                        onChange={(e) => setTranscript(e.target.value)}
                        placeholder="Trascrizione dell'audio..."
                        rows={3}
                        className="resize-none text-sm min-h-[80px] max-h-[120px]"
                      />
                    </CollapsibleContent>
                  </Collapsible>

                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleProcessWithAI}
                    disabled={isProcessing || isSaving}
                    className="w-full"
                  >
                    {isProcessing ? (
                      <>
                        <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                        Rielaborando...
                      </>
                    ) : (
                      <>
                        <FileText className="h-4 w-4 mr-2" />
                        Rielabora con AI
                      </>
                    )}
                  </Button>
                </div>
              ) : (
                <Button
                  variant="outline"
                  onClick={handleProcessWithAI}
                  disabled={isProcessing || isSaving}
                  className="w-full"
                >
                  {isProcessing ? (
                    <>
                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                      Elaborando con AI...
                    </>
                  ) : (
                    <>
                      <FileText className="h-4 w-4 mr-2" />
                      Elabora con AI
                    </>
                  )}
                </Button>
              )}
            </div>
          )}

          </div>
        </div>

        {/* Action Buttons - Fixed at bottom */}
        <div className="px-4 sm:px-6 py-3 border-t bg-background shrink-0">
          <div className="flex gap-2">
            <Button
              variant="outline"
              onClick={handleClose}
              disabled={isSaving}
              className="flex-1 h-10"
            >
              <X className="h-4 w-4 mr-2" />
              Annulla
            </Button>
            <Button
              onClick={handleSave}
              disabled={!canSave || isSaving || isProcessing}
              className="flex-1 h-10"
            >
              <Save className="h-4 w-4 mr-2" />
              {isSaving ? "Salvando..." : "Salva Nota"}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
