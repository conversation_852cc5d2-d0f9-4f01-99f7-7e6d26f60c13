import { useState, useMemo } from "react";
import { Search, Plus, Mic, FileText, Calendar, Play, Trash2 } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardHeader } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle } from "@/components/ui/alert-dialog";
import NoteRecordingDialog from "@/components/notes/NoteRecordingDialog";
import { useGetAllNotes, useCreateNote, useDeleteNote, useUploadAudio } from "@/hooks/notes/useNotes";
import { useAuth } from "@/hooks/auth/useAuth";
import { toast } from "sonner";
import { motion, AnimatePresence } from "framer-motion";
import type { NoteWithDetails, NoteSearchFilters } from "@/types/notes";

export default function Notes() {
  const [searchQuery, setSearchQuery] = useState("");
  const [isRecordingDialogOpen, setIsRecordingDialogOpen] = useState(false);
  const [noteToDelete, setNoteToDelete] = useState<string | null>(null);
  const [playingNoteId, setPlayingNoteId] = useState<string | null>(null);
  const [audioElements, setAudioElements] = useState<Map<string, HTMLAudioElement>>(new Map());

  const { user } = useAuth();

  // Create search filters
  const searchFilters: NoteSearchFilters = useMemo(() => ({
    query: searchQuery.trim() || undefined,
    sortBy: 'created_at',
    sortOrder: 'desc',
  }), [searchQuery]);

  const { data: notes = [], isLoading, error } = useGetAllNotes(searchFilters);
  const createNoteMutation = useCreateNote();
  const deleteNoteMutation = useDeleteNote();
  const uploadAudioMutation = useUploadAudio();

  // Handle note creation
  const handleCreateNote = async (noteData: {
    title: string;
    content?: string;
    audioBlob?: Blob;
    duration?: number;
    transcript?: string;
  }) => {
    try {
      let audioUrl: string | undefined;

      // First create the note
      const newNote = await createNoteMutation.mutateAsync({
        title: noteData.title,
        content: noteData.content,
        transcript: noteData.transcript,
        audio_duration: noteData.duration,
        user_id: user.id,
      });

      // If there's audio, upload it and update the note
      if (noteData.audioBlob) {
        audioUrl = await uploadAudioMutation.mutateAsync({
          audioBlob: noteData.audioBlob,
          noteId: newNote.id,
        });

        // Update note with audio URL (this would need an update mutation)
        // For now, we'll handle this in the backend or add an update call
      }

    } catch (error) {
      console.error('Error creating note:', error);
      throw error;
    }
  };

  // Handle note deletion
  const handleDeleteNote = async (noteId: string) => {
    try {
      await deleteNoteMutation.mutateAsync(noteId);
      setNoteToDelete(null);

      // Stop audio if playing
      if (playingNoteId === noteId) {
        const audio = audioElements.get(noteId);
        if (audio) {
          audio.pause();
          setPlayingNoteId(null);
        }
      }
    } catch (error) {
      console.error('Error deleting note:', error);
    }
  };

  // Handle audio playback
  const handlePlayAudio = (note: NoteWithDetails) => {
    if (!note.audio_url) return;

    const currentAudio = audioElements.get(note.id);

    if (currentAudio) {
      if (playingNoteId === note.id) {
        currentAudio.pause();
        setPlayingNoteId(null);
      } else {
        // Stop any other playing audio
        audioElements.forEach((audio, id) => {
          if (id !== note.id) {
            audio.pause();
          }
        });

        currentAudio.play();
        setPlayingNoteId(note.id);
      }
    } else {
      // Create new audio element
      const audio = new Audio(note.audio_url);
      audio.onended = () => setPlayingNoteId(null);
      audio.onerror = () => {
        toast.error("Errore nella riproduzione audio");
        setPlayingNoteId(null);
      };

      setAudioElements(prev => new Map(prev).set(note.id, audio));
      audio.play();
      setPlayingNoteId(note.id);
    }
  };

  // Format date
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return new Intl.DateTimeFormat('it-IT', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    }).format(date);
  };

  if (!user) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-center">
          <h3 className="text-lg font-semibold mb-2">Accesso richiesto</h3>
          <p className="text-muted-foreground">
            Effettua l'accesso per visualizzare le tue note
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background">
      {/* Search Bar */}
      <div className="sticky top-0 z-10 bg-background/95 backdrop-blur-sm border-b border-border px-4 py-3">
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Cerca note e trascrizioni..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10 pr-4"
          />
        </div>
      </div>

      {/* Header */}
      <div className="px-4 pt-4">
        <div className="flex items-center gap-2 mb-4">
          <div className="w-8 h-8 bg-muted rounded flex items-center justify-center">
            <FileText className="h-4 w-4 text-muted-foreground" />
          </div>
          <div className="bg-primary text-primary-foreground px-4 py-2 rounded-full">
            Tutte le note ({notes.length})
          </div>
        </div>

        {/* Content */}
        <div className="space-y-4 pb-24">
          {isLoading ? (
            // Loading skeleton
            <div className="space-y-4">
              {[...Array(3)].map((_, i) => (
                <Card key={i}>
                  <CardHeader>
                    <Skeleton className="h-4 w-3/4" />
                    <Skeleton className="h-3 w-1/2" />
                  </CardHeader>
                  <CardContent>
                    <Skeleton className="h-3 w-full mb-2" />
                    <Skeleton className="h-3 w-2/3" />
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : error ? (
            // Error state
            <div className="flex flex-col items-center justify-center py-12 text-center">
              <div className="w-16 h-16 bg-destructive/10 rounded-full flex items-center justify-center mb-4">
                <FileText className="h-8 w-8 text-destructive" />
              </div>
              <h3 className="text-lg font-semibold mb-2">Errore nel caricamento</h3>
              <p className="text-muted-foreground mb-6">
                Si è verificato un errore nel caricamento delle note
              </p>
            </div>
          ) : notes.length === 0 ? (
            // Empty state
            <div className="flex flex-col items-center justify-center py-12 text-center">
              <div className="w-16 h-16 bg-muted rounded-full flex items-center justify-center mb-4">
                <FileText className="h-8 w-8 text-muted-foreground" />
              </div>
              <h3 className="text-lg font-semibold mb-2">
                {searchQuery ? "Nessuna nota trovata" : "Nessuna nota"}
              </h3>
              <p className="text-muted-foreground mb-6">
                {searchQuery
                  ? "Prova a modificare i termini di ricerca"
                  : "Inizia creando la tua prima nota vocale"
                }
              </p>
            </div>
          ) : (
            // Notes list
            <AnimatePresence>
              {notes.map((note) => (
                <motion.div
                  key={note.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -20 }}
                  transition={{ duration: 0.2 }}
                >
                  <Card className="hover:shadow-md transition-shadow">
                    <CardHeader className="pb-3">
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <h3 className="font-semibold text-lg leading-tight mb-1">
                            {note.title}
                          </h3>
                          <div className="flex items-center gap-2 text-sm text-muted-foreground">
                            <Calendar className="h-3 w-3" />
                            {formatDate(note.created_at)}
                            {note.hasAudio && (
                              <>
                                <span>•</span>
                                <Mic className="h-3 w-3" />
                                <span>{note.formattedDuration}</span>
                              </>
                            )}
                          </div>
                        </div>
                        <div className="flex items-center gap-1">
                          {note.hasAudio && (
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handlePlayAudio(note)}
                              className="h-8 w-8 p-0"
                            >
                              <Play className={`h-4 w-4 ${playingNoteId === note.id ? 'text-primary' : ''}`} />
                            </Button>
                          )}
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => setNoteToDelete(note.id)}
                            className="h-8 w-8 p-0 text-destructive hover:text-destructive"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    </CardHeader>
                    {(note.content || note.transcript) && (
                      <CardContent className="pt-0">
                        {note.content && (
                          <p className="text-sm text-muted-foreground mb-2">
                            {note.content}
                          </p>
                        )}
                        {note.transcript && (
                          <div className="bg-muted/50 rounded-lg p-3">
                            <div className="flex items-center gap-2 mb-2">
                              <Mic className="h-3 w-3 text-muted-foreground" />
                              <span className="text-xs font-medium text-muted-foreground">
                                Trascrizione
                              </span>
                            </div>
                            <p className="text-sm">{note.transcript}</p>
                          </div>
                        )}
                        <div className="flex gap-2 mt-3">
                          {note.hasAudio && (
                            <Badge variant="secondary" className="text-xs">
                              <Mic className="h-3 w-3 mr-1" />
                              Audio
                            </Badge>
                          )}
                          {note.hasTranscript && (
                            <Badge variant="secondary" className="text-xs">
                              <FileText className="h-3 w-3 mr-1" />
                              Trascritto
                            </Badge>
                          )}
                        </div>
                      </CardContent>
                    )}
                  </Card>
                </motion.div>
              ))}
            </AnimatePresence>
          )}
        </div>
      </div>

      {/* Floating Action Button */}
      <Button
        size="lg"
        onClick={() => setIsRecordingDialogOpen(true)}
        className="fixed bottom-20 right-4 h-14 w-14 rounded-full shadow-lg"
      >
        <Plus className="h-6 w-6" />
      </Button>

      {/* Note Recording Dialog */}
      <NoteRecordingDialog
        open={isRecordingDialogOpen}
        onOpenChange={setIsRecordingDialogOpen}
        onSave={handleCreateNote}
      />

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={!!noteToDelete} onOpenChange={() => setNoteToDelete(null)}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Elimina nota</AlertDialogTitle>
            <AlertDialogDescription>
              Sei sicuro di voler eliminare questa nota? Questa azione non può essere annullata.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Annulla</AlertDialogCancel>
            <AlertDialogAction
              onClick={() => noteToDelete && handleDeleteNote(noteToDelete)}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              Elimina
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}