import { supabase } from "@/integrations/supabase/client";
import {
  AIAssistantProfile,
  AssistantDetails,
} from "@/types/aiassistantprofile";
import { queryOptions } from "@tanstack/react-query";

const sleep = (ms: number) => new Promise((resolve) => setTimeout(resolve, ms));

const fetchAssistantProfiles = async (): Promise<AIAssistantProfile[]> => {
  //  await sleep(3000);
  const { data, error } = await supabase
    .from("ai_assistant_profile")
    .select("*");
  if (error) {
    throw error;
  }

  return data || [];
};

const fetchAssistantProfilesById = async (
  selectedAssistantId: string
): Promise<AssistantDetails> => {
  //  await sleep(3000);
  const { data } = await supabase
    .from("ai_assistant_profile")
    .select("id, voice_id, name, image_url")
    .eq("id", selectedAssistantId)
    .single();

  return data;
};

function getAiAssistantProfilesQueryOptions() {
  return queryOptions({
    queryKey: ["ai_assistant_profiles"],
    queryFn: fetchAssistantProfiles,
    // staleTime: 1000 * 60 * 5, // 5 minutes
    // refetchOnWindowFocus: false,
  });
}

function getAiAssistantProfilesByIdQueryOptions(selectedAssistantId: string) {
  return queryOptions({
    queryKey: ["ai_assistant_profiles", selectedAssistantId],
    queryFn: () => fetchAssistantProfilesById(selectedAssistantId),
    // staleTime: 1000 * 60 * 5, // 5 minutes
    // refetchOnWindowFocus: false,
  });
}

export { getAiAssistantProfilesQueryOptions, getAiAssistantProfilesByIdQueryOptions };
