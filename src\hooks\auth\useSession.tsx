
import { useState, useEffect, useCallback } from "react";
import { useNavigate } from "react-router-dom";
import { supabase } from "@/integrations/supabase/client";
import { toast } from "sonner";
import { useQueryClient } from "@tanstack/react-query";

export const useSession = () => {
   const queryClient = useQueryClient();
  const navigate = useNavigate();
  const [user, setUser] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isAuthenticated, setIsAuthenticated] = useState<boolean | null>(null);

  const isTokenExpired = useCallback((session: any) => {
    if (!session?.expires_at) return true;
    const expirationTime = session.expires_at * 1000;
    const bufferTime = 5 * 60 * 1000; // 5 minuti
    return Date.now() >= (expirationTime - bufferTime);
  }, []);

  const refreshSession = useCallback(async () => {
    const currentSession = await supabase.auth.getSession();
    if (!currentSession.data.session || !isTokenExpired(currentSession.data.session)) {
      return currentSession.data.session;
    }

    try {
      const { data: { session }, error } = await supabase.auth.refreshSession();
      
      if (error) {
        console.error('Error refreshing session:', error);
        return null;
      }

      return session;
    } catch (error) {
      console.error('Error in refreshSession:', error);
      return null;
    }
  }, [isTokenExpired]);

  const handleAuthChange = useCallback(async (event: string, session: any) => {
 //   console.log('Auth state changed:', event, session?.user?.id);
    
    if (event === 'SIGNED_OUT') {
      setUser(null);
      setIsAuthenticated(false);
      // Clear all React Query cache to ensure no stale user data remains
      queryClient.clear();

      navigate("/login");
      return;
    }

    setIsAuthenticated(!!session);
    if (session?.user) {
      setUser(session.user);
    }
  }, [navigate,queryClient]);

  const initialize = useCallback(async () => {
    try {
      const { data: { session }, error } = await supabase.auth.getSession();
      
      if (error) throw error;

      if (!session) {
        setIsAuthenticated(false);
        setUser(null);
        
        const shouldRedirectToLogin = !isPublicRoute(window.location.pathname);
        if (shouldRedirectToLogin) {
          navigate("/login");
        }
        return;
      }

      const currentSession = isTokenExpired(session) 
        ? await refreshSession()
        : session;

      if (!currentSession) {
        setIsAuthenticated(false);
        setUser(null);
        navigate("/login");
        return;
      }

      setUser(currentSession.user);
      setIsAuthenticated(true);
    } catch (error) {
      console.error('Error in initialize:', error);
      toast.error("Si è verificato un errore imprevisto");
    } finally {
      setIsLoading(false);
    }
  }, [navigate, isTokenExpired, refreshSession]);

  // Helper function to determine if a route is public or requires login
  const isPublicRoute = (path: string): boolean => {
    // Public routes that don't require authentication
    const publicRoutes = [
      '/login', '/signup', '/', '/deals', '/deal', 
      '/map', '/explore', '/profile'
    ];
    
    // Routes that should show LoginRequired instead of redirecting
    const loginRequiredRoutes = [
      '/mybookings', '/conversations', '/conversations/'
    ];
    
    // Dynamic route patterns
    const isDynamicPublicRoute = 
      path.startsWith('/business/') || 
      path.startsWith('/deals/') || 
      path.startsWith('/deal/') || 
      /^\/conversations\/[^\/]+$/.test(path);
    
    return (
      publicRoutes.includes(path) || 
      loginRequiredRoutes.includes(path) || 
      isDynamicPublicRoute
    );
  };

  useEffect(() => {
    let mounted = true;
    let tokenCheckInterval: NodeJS.Timeout;

    // Configura il controllo del token solo se l'utente è autenticato
    const setupTokenCheck = () => {
      if (tokenCheckInterval) clearInterval(tokenCheckInterval);
      
      tokenCheckInterval = setInterval(async () => {
        if (!mounted) return;
        
        const { data: { session } } = await supabase.auth.getSession();
        if (session && isTokenExpired(session)) {
          await refreshSession();
        }
      }, 4 * 60 * 1000); // Controllo ogni 4 minuti invece che 5
    };

    const initializeAuth = async () => {
      if (!mounted) return;
      await initialize();
      if (isAuthenticated) {
        setupTokenCheck();
      }
    };

    const { data: { subscription } } = supabase.auth.onAuthStateChange(handleAuthChange);
    
    initializeAuth();

    return () => {
      mounted = false;
      if (tokenCheckInterval) clearInterval(tokenCheckInterval);
      subscription.unsubscribe();
    };
  }, [initialize, handleAuthChange, isTokenExpired, refreshSession, isAuthenticated]);

  return {
    user,
    isLoading,
    isAuthenticated,
  };
};
