-- Create storage bucket for note recordings
INSERT INTO storage.buckets (id, name, public)
VALUES ('note-recordings', 'note-recordings', false);

-- Policy: Users can view their own recordings
CREATE POLICY "Users can view their own recordings"
ON storage.objects FOR SELECT
USING (
  bucket_id = 'note-recordings' 
  AND auth.uid()::text = (storage.foldername(name))[1]
);

-- Policy: Users can upload their own recordings
CREATE POLICY "Users can upload their own recordings"
ON storage.objects FOR INSERT
WITH CHECK (
  bucket_id = 'note-recordings' 
  AND auth.uid()::text = (storage.foldername(name))[1]
);

-- Policy: Users can update their own recordings
CREATE POLICY "Users can update their own recordings"
ON storage.objects FOR UPDATE
USING (
  bucket_id = 'note-recordings' 
  AND auth.uid()::text = (storage.foldername(name))[1]
);

-- Policy: Users can delete their own recordings
CREATE POLICY "Users can delete their own recordings"
ON storage.objects FOR DELETE
USING (
  bucket_id = 'note-recordings' 
  AND auth.uid()::text = (storage.foldername(name))[1]
);