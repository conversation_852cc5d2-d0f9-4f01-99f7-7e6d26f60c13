import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { toast } from 'sonner';
import type { 
  Note, 
  CreateNoteData, 
  UpdateNoteData, 
  NoteWithDetails, 
  NoteSearchFilters,
  NotesListResponse 
} from '@/types/notes';

// Hook to get all notes for the current user
export const useGetAllNotes = (filters?: NoteSearchFilters) => {
  return useQuery({
    queryKey: ['notes', filters],
    queryFn: async (): Promise<NoteWithDetails[]> => {
      const { data: { user } } = await supabase.auth.getUser();
      
      if (!user) {
        throw new Error('Utente non autenticato');
      }

      let query = supabase
        .from('notes')
        .select('*')
        .eq('user_id', user.id);

      // Apply filters
      if (filters?.query) {
        // Search in title, content, and transcript
        query = query.or(`title.ilike.%${filters.query}%,content.ilike.%${filters.query}%,transcript.ilike.%${filters.query}%`);
      }

      if (filters?.hasAudio !== undefined) {
        if (filters.hasAudio) {
          query = query.not('audio_url', 'is', null);
        } else {
          query = query.is('audio_url', null);
        }
      }

      if (filters?.hasTranscript !== undefined) {
        if (filters.hasTranscript) {
          query = query.not('transcript', 'is', null);
        } else {
          query = query.is('transcript', null);
        }
      }

      if (filters?.dateFrom) {
        query = query.gte('created_at', filters.dateFrom.toISOString());
      }

      if (filters?.dateTo) {
        query = query.lte('created_at', filters.dateTo.toISOString());
      }

      // Apply sorting
      const sortBy = filters?.sortBy || 'created_at';
      const sortOrder = filters?.sortOrder || 'desc';
      query = query.order(sortBy, { ascending: sortOrder === 'asc' });

      const { data, error } = await query;

      if (error) {
        console.error('Errore nel recupero delle note:', error);
        throw error;
      }

      // Transform to NoteWithDetails
      const notesWithDetails: NoteWithDetails[] = (data || []).map(note => ({
        ...note,
        hasAudio: !!note.audio_url,
        hasTranscript: !!note.transcript,
        formattedDuration: note.audio_duration ? formatDuration(note.audio_duration) : undefined,
        searchableContent: [note.title, note.content, note.transcript].filter(Boolean).join(' '),
      }));

      return notesWithDetails;
    }
  });
};

// Hook to get a single note by ID
export const useGetNote = (noteId: string) => {
  return useQuery({
    queryKey: ['notes', noteId],
    queryFn: async (): Promise<NoteWithDetails | null> => {
      const { data: { user } } = await supabase.auth.getUser();
      
      if (!user) {
        throw new Error('Utente non autenticato');
      }

      const { data, error } = await supabase
        .from('notes')
        .select('*')
        .eq('id', noteId)
        .eq('user_id', user.id)
        .single();

      if (error) {
        if (error.code === 'PGRST116') {
          return null; // Note not found
        }
        console.error('Errore nel recupero della nota:', error);
        throw error;
      }

      // Transform to NoteWithDetails
      const noteWithDetails: NoteWithDetails = {
        ...data,
        hasAudio: !!data.audio_url,
        hasTranscript: !!data.transcript,
        formattedDuration: data.audio_duration ? formatDuration(data.audio_duration) : undefined,
        searchableContent: [data.title, data.content, data.transcript].filter(Boolean).join(' '),
      };

      return noteWithDetails;
    },
    enabled: !!noteId,
  });
};

// Hook to create a new note
export const useCreateNote = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (noteData: CreateNoteData): Promise<Note> => {
      const { data: { user } } = await supabase.auth.getUser();
      
      if (!user) {
        throw new Error('Utente non autenticato');
      }

      const { data, error } = await supabase
        .from('notes')
        .insert({
          ...noteData,
          user_id: user.id,
        })
        .select()
        .single();

      if (error) {
        console.error('Errore nella creazione della nota:', error);
        throw error;
      }

      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['notes'] });
      toast.success('Nota creata con successo');
    },
    onError: (error) => {
      console.error('Errore nella creazione della nota:', error);
      toast.error('Errore nella creazione della nota');
    },
  });
};

// Hook to update a note
export const useUpdateNote = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ id, ...updateData }: UpdateNoteData & { id: string }): Promise<Note> => {
      const { data: { user } } = await supabase.auth.getUser();
      
      if (!user) {
        throw new Error('Utente non autenticato');
      }

      const { data, error } = await supabase
        .from('notes')
        .update(updateData)
        .eq('id', id)
        .eq('user_id', user.id)
        .select()
        .single();

      if (error) {
        console.error('Errore nell\'aggiornamento della nota:', error);
        throw error;
      }

      return data;
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ['notes'] });
      queryClient.invalidateQueries({ queryKey: ['notes', data.id] });
      toast.success('Nota aggiornata con successo');
    },
    onError: (error) => {
      console.error('Errore nell\'aggiornamento della nota:', error);
      toast.error('Errore nell\'aggiornamento della nota');
    },
  });
};

// Hook to delete a note
export const useDeleteNote = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (noteId: string): Promise<void> => {
      const { data: { user } } = await supabase.auth.getUser();
      
      if (!user) {
        throw new Error('Utente non autenticato');
      }

      const { error } = await supabase
        .from('notes')
        .delete()
        .eq('id', noteId)
        .eq('user_id', user.id);

      if (error) {
        console.error('Errore nell\'eliminazione della nota:', error);
        throw error;
      }
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['notes'] });
      toast.success('Nota eliminata con successo');
    },
    onError: (error) => {
      console.error('Errore nell\'eliminazione della nota:', error);
      toast.error('Errore nell\'eliminazione della nota');
    },
  });
};

// Hook to upload audio file to Supabase Storage
export const useUploadAudio = () => {
  return useMutation({
    mutationFn: async ({ audioBlob, noteId }: { audioBlob: Blob; noteId: string }): Promise<string> => {
      const { data: { user } } = await supabase.auth.getUser();
      
      if (!user) {
        throw new Error('Utente non autenticato');
      }

      const fileName = `${user.id}/${noteId}.webm`;
      
      const { data, error } = await supabase.storage
        .from('note-recordings')
        .upload(fileName, audioBlob, {
          contentType: 'audio/webm',
          upsert: true,
        });

      if (error) {
        console.error('Errore nel caricamento audio:', error);
        throw error;
      }

      // Get public URL
      const { data: urlData } = supabase.storage
        .from('note-recordings')
        .getPublicUrl(fileName);

      return urlData.publicUrl;
    },
    onError: (error) => {
      console.error('Errore nel caricamento audio:', error);
      toast.error('Errore nel caricamento dell\'audio');
    },
  });
};

// Utility function to format duration
const formatDuration = (seconds: number): string => {
  const mins = Math.floor(seconds / 60);
  const secs = seconds % 60;
  return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
};
