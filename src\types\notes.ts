import type { Database } from "@/integrations/supabase/types";

// Base Note type from database
export type Note = Database['public']['Tables']['notes']['Row'];

// Note creation data (for inserts)
export type CreateNoteData = Database['public']['Tables']['notes']['Insert'];

// Note update data (for updates)
export type UpdateNoteData = Database['public']['Tables']['notes']['Update'];

// Extended Note type with additional computed properties
export type NoteWithDetails = Note & {
  // Computed properties
  hasAudio: boolean;
  hasTranscript: boolean;
  formattedDuration?: string;
  searchableContent: string; // Combined title, content, and transcript for search
};

// Voice recording states
export type RecordingState = 'idle' | 'recording' | 'paused' | 'stopped' | 'processing';

// Audio recording data
export interface AudioRecording {
  blob: Blob;
  url: string;
  duration: number; // in seconds
  base64?: string;
}

// Note recording session data
export interface NoteRecordingSession {
  id: string;
  title: string;
  content?: string;
  recording?: AudioRecording;
  transcript?: string;
  state: RecordingState;
  startTime?: Date;
  endTime?: Date;
}

// Search filters for notes
export interface NoteSearchFilters {
  query?: string;
  hasAudio?: boolean;
  hasTranscript?: boolean;
  dateFrom?: Date;
  dateTo?: Date;
  sortBy?: 'created_at' | 'updated_at' | 'title';
  sortOrder?: 'asc' | 'desc';
}

// Note list response with pagination
export interface NotesListResponse {
  notes: NoteWithDetails[];
  total: number;
  hasMore: boolean;
  nextCursor?: string;
}

// Voice recording hook return type
export interface UseVoiceRecordingReturn {
  isRecording: boolean;
  isPaused: boolean;
  duration: number;
  audioBlob: Blob | null;
  audioUrl: string | null;
  error: string | null;
  startRecording: () => Promise<void>;
  stopRecording: () => void;
  pauseRecording: () => void;
  resumeRecording: () => void;
  resetRecording: () => void;
  getBase64Audio: () => Promise<string | null>;
}

// Transcription service response
export interface TranscriptionResponse {
  transcript: string;
  confidence?: number;
  language?: string;
  duration?: number;
}

// Note creation form data
export interface NoteFormData {
  title: string;
  content?: string;
  audioFile?: File;
  transcript?: string;
}

// Note export formats
export type NoteExportFormat = 'json' | 'txt' | 'md' | 'pdf';

// Note sharing options
export interface NoteSharingOptions {
  includeAudio: boolean;
  includeTranscript: boolean;
  format: NoteExportFormat;
}
