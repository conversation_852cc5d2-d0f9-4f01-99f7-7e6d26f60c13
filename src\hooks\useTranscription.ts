import { useMutation } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { toast } from 'sonner';
import type { TranscriptionResponse } from '@/types/notes';

interface TranscribeAudioParams {
  audioBlob: Blob;
  language?: string;
}

export const useTranscription = () => {
  return useMutation({
    mutationFn: async ({ audioBlob, language = 'it' }: TranscribeAudioParams): Promise<TranscriptionResponse> => {
      // Convert blob to base64
      const base64Audio = await new Promise<string>((resolve, reject) => {
        const reader = new FileReader();
        reader.onloadend = () => {
          const base64 = reader.result as string;
          const base64Only = base64.split(',')[1];
          resolve(base64Only);
        };
        reader.onerror = reject;
        reader.readAsDataURL(audioBlob);
      });

      // Call the speech-to-text edge function
      const { data, error } = await supabase.functions.invoke('speech-to-text', {
        body: { 
          audioData: base64Audio,
          language 
        }
      });

      if (error) {
        console.error('Transcription error:', error);
        throw new Error('Errore nella trascrizione audio');
      }

      if (!data?.transcript) {
        throw new Error('Nessuna trascrizione ricevuta');
      }

      return {
        transcript: data.transcript,
        language: data.language,
        duration: data.duration,
      };
    },
    onError: (error) => {
      console.error('Transcription error:', error);
      toast.error('Errore nella trascrizione audio');
    },
  });
};
